import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { LimboGameState, User } from '@/types';
import { formatCurrency } from '@/lib/utils';
import { Target, TrendingUp, Percent, DollarSign } from 'lucide-react';

interface LimboControlsProps {
  user: User;
  gameState: LimboGameState | null;
  onPlaceBet: (betAmount: number, targetMultiplier: number) => Promise<void>;
  loading: boolean;
  canPlaceBet: boolean;
  calculateWinChance: (targetMultiplier: number) => number;
  calculatePayout: (betAmount: number, targetMultiplier: number) => number;
}

export function LimboControls({
  user,
  gameState,
  onPlaceBet,
  loading,
  canPlaceBet,
  calculateWinChance,
  calculatePayout
}: LimboControlsProps) {
  console.log('🎯 LimboControls: Component rendered with props:', {
    user: user?.id,
    gameState: gameState?.id,
    loading,
    canPlaceBet
  });

  const [betAmount, setBetAmount] = useState<string>('1.00');
  const [targetMultiplier, setTargetMultiplier] = useState<string>('2.00');
  const [autoBet, setAutoBet] = useState(false);

  // Parse numeric values
  const numericBetAmount = parseFloat(betAmount) || 0;
  const numericTargetMultiplier = parseFloat(targetMultiplier) || 1.01;

  // Calculate statistics
  const winChance = calculateWinChance(numericTargetMultiplier);
  const potentialPayout = calculatePayout(numericBetAmount, numericTargetMultiplier);
  const potentialProfit = potentialPayout - numericBetAmount;

  // Validation
  const isValidBet = numericBetAmount >= 0.01 && numericBetAmount <= user.usdt_balance;
  const isValidTarget = numericTargetMultiplier >= 1.01 && numericTargetMultiplier <= 1000;
  const canSubmit = isValidBet && isValidTarget && canPlaceBet && !loading;

  const handlePlaceBet = async () => {
    console.log('🎯 LimboControls: Button clicked!', {
      canSubmit,
      isValidBet,
      isValidTarget,
      canPlaceBet,
      loading,
      numericBetAmount,
      numericTargetMultiplier
    });

    if (!canSubmit) {
      console.log('🎯 LimboControls: Cannot submit bet - validation failed');
      return;
    }

    try {
      console.log('🎯 LimboControls: Calling onPlaceBet...');
      await onPlaceBet(numericBetAmount, numericTargetMultiplier);
      console.log('🎯 LimboControls: onPlaceBet completed');
    } catch (error) {
      console.error('🎯 LimboControls: Error placing bet:', error);
    }
  };

  const handleQuickBet = (multiplier: number) => {
    setTargetMultiplier(multiplier.toFixed(2));
  };

  const handleQuickAmount = (amount: number) => {
    setBetAmount(amount.toFixed(2));
  };

  const handleMaxBet = () => {
    setBetAmount(user.usdt_balance.toFixed(2));
  };

  const handleHalfBet = () => {
    const half = numericBetAmount / 2;
    setBetAmount(Math.max(0.01, half).toFixed(2));
  };

  const handleDoubleBet = () => {
    const double = numericBetAmount * 2;
    setBetAmount(Math.min(user.usdt_balance, double).toFixed(2));
  };

  return (
    <Card className="bg-gray-800/80 border-gray-600 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center space-x-2 text-white">
          <Target className="h-5 w-5 text-orange-400" />
          <span>Limbo Controls</span>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Bet Amount */}
        <div className="space-y-2">
          <Label htmlFor="bet-amount" className="text-gray-300">
            Bet Amount
          </Label>
          <div className="space-y-2">
            <Input
              id="bet-amount"
              type="number"
              value={betAmount}
              onChange={(e) => setBetAmount(e.target.value)}
              placeholder="0.00"
              min="0.01"
              max={user.usdt_balance}
              step="0.01"
              className="bg-gray-700 border-gray-600 text-white"
              disabled={loading}
            />
            
            {/* Quick bet buttons */}
            <div className="flex space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={handleHalfBet}
                className="flex-1 text-xs bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
                disabled={loading}
              >
                ½
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDoubleBet}
                className="flex-1 text-xs bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
                disabled={loading}
              >
                2×
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleMaxBet}
                className="flex-1 text-xs bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
                disabled={loading}
              >
                Max
              </Button>
            </div>
          </div>
          
          {!isValidBet && (
            <p className="text-red-400 text-sm">
              {numericBetAmount < 0.01 ? 'Minimum bet is 0.01' : 'Insufficient balance'}
            </p>
          )}
        </div>

        {/* Target Multiplier */}
        <div className="space-y-2">
          <Label htmlFor="target-multiplier" className="text-gray-300">
            Target Multiplier
          </Label>
          <div className="space-y-2">
            <Input
              id="target-multiplier"
              type="number"
              value={targetMultiplier}
              onChange={(e) => setTargetMultiplier(e.target.value)}
              placeholder="1.01"
              min="1.01"
              max="1000"
              step="0.01"
              className="bg-gray-700 border-gray-600 text-white"
              disabled={loading}
            />
            
            {/* Quick multiplier buttons */}
            <div className="grid grid-cols-4 gap-1">
              {[1.5, 2, 5, 10].map((mult) => (
                <Button
                  key={mult}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickBet(mult)}
                  className="text-xs bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
                  disabled={loading}
                >
                  {mult}×
                </Button>
              ))}
            </div>
          </div>
          
          {!isValidTarget && (
            <p className="text-red-400 text-sm">
              Target must be between 1.01× and 1000×
            </p>
          )}
        </div>

        <Separator className="bg-gray-600" />

        {/* Statistics */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-400 text-sm flex items-center">
              <Percent className="h-4 w-4 mr-1" />
              Win Chance
            </span>
            <Badge variant="secondary" className="bg-blue-600/20 text-blue-400">
              {winChance.toFixed(2)}%
            </Badge>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-400 text-sm flex items-center">
              <DollarSign className="h-4 w-4 mr-1" />
              Payout
            </span>
            <span className="text-green-400 font-medium">
              {formatCurrency(potentialPayout)}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-400 text-sm flex items-center">
              <TrendingUp className="h-4 w-4 mr-1" />
              Profit
            </span>
            <span className={`font-medium ${potentialProfit > 0 ? 'text-green-400' : 'text-gray-400'}`}>
              +{formatCurrency(potentialProfit)}
            </span>
          </div>
        </div>

        <Separator className="bg-gray-600" />

        {/* Place Bet Button */}
        <Button
          onClick={handlePlaceBet}
          disabled={!canSubmit}
          className="w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3"
          size="lg"
        >
          {loading ? (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Placing Bet...</span>
            </div>
          ) : (
            `Place Bet - ${formatCurrency(numericBetAmount)}`
          )}
        </Button>

        {/* Balance Display */}
        <div className="text-center">
          <p className="text-gray-400 text-sm">
            Balance: <span className="text-white font-medium">{formatCurrency(user.usdt_balance)}</span>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
