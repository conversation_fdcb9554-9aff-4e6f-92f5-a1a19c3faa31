"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/game/[gameType]",{

/***/ "(pages-dir-browser)/./components/game/limbo/LimboControls.tsx":
/*!*************************************************!*\
  !*** ./components/game/limbo/LimboControls.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LimboControls: () => (/* binding */ LimboControls)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(pages-dir-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(pages-dir-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(pages-dir-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(pages-dir-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-browser)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,Percent,Target,TrendingUp!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=DollarSign,Percent,Target,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction LimboControls(param) {\n    let { user, gameState, onPlaceBet, loading, canPlaceBet, calculateWinChance, calculatePayout } = param;\n    _s();\n    console.log('🎯 LimboControls: Component rendered with props:', {\n        user: user === null || user === void 0 ? void 0 : user.id,\n        gameState: gameState === null || gameState === void 0 ? void 0 : gameState.id,\n        loading,\n        canPlaceBet\n    });\n    const [betAmount, setBetAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('1.00');\n    const [targetMultiplier, setTargetMultiplier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('2.00');\n    const [autoBet, setAutoBet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Parse numeric values\n    const numericBetAmount = parseFloat(betAmount) || 0;\n    const numericTargetMultiplier = parseFloat(targetMultiplier) || 1.01;\n    // Calculate statistics\n    const winChance = calculateWinChance(numericTargetMultiplier);\n    const potentialPayout = calculatePayout(numericBetAmount, numericTargetMultiplier);\n    const potentialProfit = potentialPayout - numericBetAmount;\n    // Validation\n    const isValidBet = numericBetAmount >= 0.01 && numericBetAmount <= user.usdt_balance;\n    const isValidTarget = numericTargetMultiplier >= 1.01 && numericTargetMultiplier <= 1000;\n    const canSubmit = isValidBet && isValidTarget && canPlaceBet && !loading;\n    const handlePlaceBet = async ()=>{\n        console.log('🎯 LimboControls: Button clicked!', {\n            canSubmit,\n            isValidBet,\n            isValidTarget,\n            canPlaceBet,\n            loading,\n            numericBetAmount,\n            numericTargetMultiplier\n        });\n        if (!canSubmit) {\n            console.log('🎯 LimboControls: Cannot submit bet - validation failed');\n            return;\n        }\n        try {\n            console.log('🎯 LimboControls: Calling onPlaceBet...');\n            await onPlaceBet(numericBetAmount, numericTargetMultiplier);\n            console.log('🎯 LimboControls: onPlaceBet completed');\n        } catch (error) {\n            console.error('🎯 LimboControls: Error placing bet:', error);\n        }\n    };\n    const handleQuickBet = (multiplier)=>{\n        setTargetMultiplier(multiplier.toFixed(2));\n    };\n    const handleQuickAmount = (amount)=>{\n        setBetAmount(amount.toFixed(2));\n    };\n    const handleMaxBet = ()=>{\n        setBetAmount(user.usdt_balance.toFixed(2));\n    };\n    const handleHalfBet = ()=>{\n        const half = numericBetAmount / 2;\n        setBetAmount(Math.max(0.01, half).toFixed(2));\n    };\n    const handleDoubleBet = ()=>{\n        const double = numericBetAmount * 2;\n        setBetAmount(Math.min(user.usdt_balance, double).toFixed(2));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"bg-gray-800/80 border-gray-600 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                className: \"pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                    className: \"flex items-center space-x-2 text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Target, {\n                            className: \"h-5 w-5 text-orange-400\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Limbo Controls\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"bet-amount\",\n                                className: \"text-gray-300\",\n                                children: \"Bet Amount\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"bet-amount\",\n                                        type: \"number\",\n                                        value: betAmount,\n                                        onChange: (e)=>setBetAmount(e.target.value),\n                                        placeholder: \"0.00\",\n                                        min: \"0.01\",\n                                        max: user.usdt_balance,\n                                        step: \"0.01\",\n                                        className: \"bg-gray-700 border-gray-600 text-white\",\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleHalfBet,\n                                                className: \"flex-1 text-xs bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600\",\n                                                disabled: loading,\n                                                children: \"\\xbd\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleDoubleBet,\n                                                className: \"flex-1 text-xs bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600\",\n                                                disabled: loading,\n                                                children: \"2\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleMaxBet,\n                                                className: \"flex-1 text-xs bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600\",\n                                                disabled: loading,\n                                                children: \"Max\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            !isValidBet && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-400 text-sm\",\n                                children: numericBetAmount < 0.01 ? 'Minimum bet is 0.01' : 'Insufficient balance'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"target-multiplier\",\n                                className: \"text-gray-300\",\n                                children: \"Target Multiplier\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"target-multiplier\",\n                                        type: \"number\",\n                                        value: targetMultiplier,\n                                        onChange: (e)=>setTargetMultiplier(e.target.value),\n                                        placeholder: \"1.01\",\n                                        min: \"1.01\",\n                                        max: \"1000\",\n                                        step: \"0.01\",\n                                        className: \"bg-gray-700 border-gray-600 text-white\",\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-1\",\n                                        children: [\n                                            1.5,\n                                            2,\n                                            5,\n                                            10\n                                        ].map((mult)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleQuickBet(mult),\n                                                className: \"text-xs bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600\",\n                                                disabled: loading,\n                                                children: [\n                                                    mult,\n                                                    \"\\xd7\"\n                                                ]\n                                            }, mult, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            !isValidTarget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-400 text-sm\",\n                                children: \"Target must be between 1.01\\xd7 and 1000\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                        className: \"bg-gray-600\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Percent, {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Win Chance\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"bg-blue-600/20 text-blue-400\",\n                                        children: [\n                                            winChance.toFixed(2),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__.DollarSign, {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Payout\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-400 font-medium\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(potentialPayout)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__.TrendingUp, {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Profit\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium \".concat(potentialProfit > 0 ? 'text-green-400' : 'text-gray-400'),\n                                        children: [\n                                            \"+\",\n                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(potentialProfit)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                        className: \"bg-gray-600\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: handlePlaceBet,\n                        disabled: !canSubmit,\n                        className: \"w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3\",\n                        size: \"lg\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Placing Bet...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this) : \"Place Bet - \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(numericBetAmount))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: [\n                                \"Balance: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-medium\",\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(user.usdt_balance)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 22\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\game\\\\limbo\\\\LimboControls.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(LimboControls, \"ifwSUNphWvDFHCxgrd7xJwoZI00=\");\n_c = LimboControls;\nvar _c;\n$RefreshReg$(_c, \"LimboControls\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/game/limbo/LimboControls.tsx\n"));

/***/ })

});