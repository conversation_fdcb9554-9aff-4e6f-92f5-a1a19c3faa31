"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/game/[gameType]",{

/***/ "(pages-dir-browser)/./pages/game/[gameType].tsx":
/*!***********************************!*\
  !*** ./pages/game/[gameType].tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GamePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(pages-dir-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/MinesGameContext */ \"(pages-dir-browser)/./contexts/MinesGameContext.tsx\");\n/* harmony import */ var _contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/DiceGameContext */ \"(pages-dir-browser)/./contexts/DiceGameContext.tsx\");\n/* harmony import */ var _contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/CrashGameContext */ \"(pages-dir-browser)/./contexts/CrashGameContext.tsx\");\n/* harmony import */ var _contexts_LimboGameContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/LimboGameContext */ \"(pages-dir-browser)/./contexts/LimboGameContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_game_GameGrid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/game/GameGrid */ \"(pages-dir-browser)/./components/game/GameGrid.tsx\");\n/* harmony import */ var _components_game_GameControls__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/game/GameControls */ \"(pages-dir-browser)/./components/game/GameControls.tsx\");\n/* harmony import */ var _components_game_dice_DiceControls__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/game/dice/DiceControls */ \"(pages-dir-browser)/./components/game/dice/DiceControls.tsx\");\n/* harmony import */ var _components_game_dice_DiceDisplay__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/game/dice/DiceDisplay */ \"(pages-dir-browser)/./components/game/dice/DiceDisplay.tsx\");\n/* harmony import */ var _components_game_crash_CrashControls__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/game/crash/CrashControls */ \"(pages-dir-browser)/./components/game/crash/CrashControls.tsx\");\n/* harmony import */ var _components_game_crash_CrashDisplay__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/game/crash/CrashDisplay */ \"(pages-dir-browser)/./components/game/crash/CrashDisplay.tsx\");\n/* harmony import */ var _components_game_limbo_LimboControls__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/game/limbo/LimboControls */ \"(pages-dir-browser)/./components/game/limbo/LimboControls.tsx\");\n/* harmony import */ var _components_game_limbo_LimboDisplay__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/game/limbo/LimboDisplay */ \"(pages-dir-browser)/./components/game/limbo/LimboDisplay.tsx\");\n/* harmony import */ var _components_wallet_WalletModal__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/wallet/WalletModal */ \"(pages-dir-browser)/./components/wallet/WalletModal.tsx\");\n/* harmony import */ var _components_game_GameHistory__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/game/GameHistory */ \"(pages-dir-browser)/./components/game/GameHistory.tsx\");\n/* harmony import */ var _components_game_LiveStats__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/game/LiveStats */ \"(pages-dir-browser)/./components/game/LiveStats.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Gem,History,LogOut,Volume2,VolumeX,Wallet!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=ArrowLeft,BarChart3,Gem,History,LogOut,Volume2,VolumeX,Wallet!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-browser)/./lib/utils.ts\");\n/* harmony import */ var _lib_sounds__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/lib/sounds */ \"(pages-dir-browser)/./lib/sounds.ts\");\n/* harmony import */ var _components_game_GameMessage__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/game/GameMessage */ \"(pages-dir-browser)/./components/game/GameMessage.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/toaster */ \"(pages-dir-browser)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(pages-dir-browser)/./components/ui/use-toast.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GamePage() {\n    var _gameState_id, _gameState_id1, _gameState_id2;\n    _s();\n    const { user, logout, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const minesGameContext = (0,_contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_4__.useMinesGame)();\n    const diceGameContext = (0,_contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_5__.useDiceGame)();\n    const crashGameContext = (0,_contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_6__.useCrashGame)();\n    const limboGameContext = (0,_contexts_LimboGameContext__WEBPACK_IMPORTED_MODULE_7__.useLimboGame)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_25__.useToast)();\n    const { gameType } = router.query;\n    console.log('🎮 GamePage: Current gameType:', gameType, typeof gameType);\n    // Get the appropriate game context based on game type\n    const currentGameContext = gameType === 'dice' ? diceGameContext : gameType === 'crash' ? crashGameContext : gameType === 'limbo' ? limboGameContext : minesGameContext;\n    const { gameState, loading: gameLoading } = currentGameContext;\n    // UI state\n    const [showHistory, setShowHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWallet, setShowWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStats, setShowStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [statsRefreshTrigger, setStatsRefreshTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [soundEnabled, setSoundEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [gameConfig, setGameConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Game message state\n    const [gameMessage, setGameMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GamePage.useEffect\": ()=>{\n            if (!authLoading && !user) {\n                router.push('/login');\n            }\n        }\n    }[\"GamePage.useEffect\"], [\n        user,\n        authLoading,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GamePage.useEffect\": ()=>{\n            _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.setEnabled(soundEnabled);\n        }\n    }[\"GamePage.useEffect\"], [\n        soundEnabled\n    ]);\n    // Initialize session storage when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GamePage.useEffect\": ()=>{\n            _lib_utils__WEBPACK_IMPORTED_MODULE_21__.SessionStorage.initializeSession();\n        }\n    }[\"GamePage.useEffect\"], []);\n    // Load game configuration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GamePage.useEffect\": ()=>{\n            if (gameType && typeof gameType === 'string') {\n                loadGameConfig(gameType);\n            }\n        }\n    }[\"GamePage.useEffect\"], [\n        gameType\n    ]);\n    const loadGameConfig = async (type)=>{\n        try {\n            const response = await fetch(\"/api/game/list?active=true\");\n            const data = await response.json();\n            if (data.success) {\n                const config = data.games.find((game)=>game.id === type);\n                if (config) {\n                    setGameConfig(config);\n                } else {\n                    // Game not found, redirect to lobby\n                    toast({\n                        title: \"Game Not Found\",\n                        description: \"The requested game is not available.\",\n                        variant: \"destructive\"\n                    });\n                    router.push('/lobby');\n                }\n            }\n        } catch (error) {\n            console.error('Failed to load game config:', error);\n            router.push('/lobby');\n        }\n    };\n    // Function to show game messages\n    const showGameMessage = (type, multiplier, profit)=>{\n        setGameMessage({\n            type,\n            visible: true,\n            multiplier,\n            profit\n        });\n    };\n    const hideGameMessage = ()=>{\n        setGameMessage(null);\n    };\n    const triggerStatsRefresh = ()=>{\n        setStatsRefreshTrigger((prev)=>prev + 1);\n    };\n    const handleStartGame = async (betAmount, mineCount)=>{\n        try {\n            const success = await minesGameContext.startGame(betAmount, mineCount);\n            if (success) {\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('click');\n            }\n        } catch (error) {\n            console.error('Failed to start game:', error);\n        }\n    };\n    // Dice game handlers\n    const handleStartDiceGame = async (betAmount, targetNumber, rollUnder)=>{\n        try {\n            const success = await diceGameContext.startGame(betAmount, targetNumber, rollUnder);\n            if (success) {\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('click');\n            }\n        } catch (error) {\n            console.error('Failed to start dice game:', error);\n        }\n    };\n    const handleRollDice = async ()=>{\n        try {\n            const result = await diceGameContext.rollDice();\n            if (result.won) {\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('win');\n                setTimeout(()=>{\n                    showGameMessage('win', result.multiplier, result.profit);\n                    triggerStatsRefresh();\n                }, 500);\n            } else {\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('lose');\n                setTimeout(()=>{\n                    showGameMessage('lost', result.multiplier, result.profit);\n                    triggerStatsRefresh();\n                }, 500);\n            }\n        } catch (error) {\n            console.error('Failed to roll dice:', error);\n        }\n    };\n    // Crash game handlers\n    const handleStartCrashGame = async (betAmount, autoCashOut)=>{\n        try {\n            const success = await crashGameContext.startGame(betAmount, autoCashOut);\n            if (success) {\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('click');\n            }\n        } catch (error) {\n            console.error('Failed to start crash game:', error);\n        }\n    };\n    const handleCrashCashOut = async ()=>{\n        try {\n            const result = await crashGameContext.cashOut();\n            if (result.success) {\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('cashout');\n                setTimeout(()=>{\n                    showGameMessage('cashout', result.multiplier, result.profit);\n                    triggerStatsRefresh();\n                }, 200);\n                toast({\n                    title: \"Successfully Cashed Out!\",\n                    description: \"Profit: +\".concat(result.profit.toFixed(2), \" USDT at \").concat(result.multiplier.toFixed(2), \"x\"),\n                    variant: \"success\",\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            console.error('Failed to cash out:', error);\n        }\n    };\n    // Limbo game handlers\n    const handleLimboPlaceBet = async (betAmount, targetMultiplier)=>{\n        try {\n            const result = await limboGameContext.placeBet(betAmount, targetMultiplier);\n            if (result.success) {\n                if (result.won) {\n                    _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('win');\n                    showGameMessage('win', targetMultiplier, result.profit);\n                } else {\n                    _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('lose');\n                    showGameMessage('lose', result.result, result.profit);\n                }\n                refreshUserData();\n            }\n        } catch (error) {\n            console.error('Failed to place limbo bet:', error);\n        }\n    };\n    const handleCellClick = async (cellIndex)=>{\n        if (!gameState || gameState.status !== 'active' || gameLoading) return;\n        const revealedCells = (gameState === null || gameState === void 0 ? void 0 : gameState.revealed_cells) || [];\n        if (revealedCells.includes(cellIndex)) return;\n        try {\n            const result = await minesGameContext.revealCell(cellIndex);\n            if (result.hit) {\n                // Mine hit - show boom message\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('mine');\n                setTimeout(()=>{\n                    _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('lose');\n                    showGameMessage('boom');\n                    // Refresh stats after game ends\n                    triggerStatsRefresh();\n                }, 300);\n            } else {\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('reveal');\n                if (result.gameOver) {\n                    // Perfect win - found all safe cells\n                    setTimeout(()=>{\n                        _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('win');\n                        showGameMessage('perfect_win', result.multiplier, result.profit || gameState.profit);\n                        // Refresh stats after game ends\n                        triggerStatsRefresh();\n                    }, 500);\n                } else {\n                    // Safe cell revealed - only show subtle toast for multiplier updates, no full-screen message\n                    if (result.multiplier > 1.5) {\n                        var _gameState_profit;\n                        toast({\n                            title: \"\".concat(result.multiplier.toFixed(2), \"x Multiplier!\"),\n                            description: \"Potential profit: \".concat((_gameState_profit = gameState.profit) === null || _gameState_profit === void 0 ? void 0 : _gameState_profit.toFixed(2), \" USDT\"),\n                            variant: \"info\",\n                            duration: 2000\n                        });\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Failed to reveal cell:', error);\n        }\n    };\n    const handleCashOut = async ()=>{\n        if (!gameState || gameState.status !== 'active') return;\n        try {\n            const result = await minesGameContext.cashOut();\n            if (result.success) {\n                _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('cashout');\n                // Show big win message for high multipliers, otherwise show regular cashout\n                const isBigWin = gameState.current_multiplier >= 5.0;\n                setTimeout(()=>{\n                    if (isBigWin) {\n                        showGameMessage('big_win', gameState.current_multiplier, result.profit);\n                    } else {\n                        showGameMessage('cashout', gameState.current_multiplier, result.profit);\n                    }\n                    // Refresh stats after cashout\n                    triggerStatsRefresh();\n                }, 200);\n                // Also show a success toast\n                toast({\n                    title: \"Successfully Cashed Out!\",\n                    description: \"Profit: +\".concat(result.profit.toFixed(2), \" USDT\"),\n                    variant: \"success\",\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            console.error('Failed to cash out:', error);\n        }\n    };\n    const refreshUserData = async ()=>{\n        // This would typically refresh user data from the auth context\n        // For now, we'll just close the wallet modal\n        setShowWallet(false);\n    };\n    const handleBackToLobby = ()=>{\n        _lib_sounds__WEBPACK_IMPORTED_MODULE_22__.soundManager.play('click');\n        router.push('/lobby');\n    };\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-white\"\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 332,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null; // Will redirect to login\n    }\n    // Show loading while game config is being loaded\n    if (!gameConfig) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-white\"\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 345,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n            lineNumber: 344,\n            columnNumber: 7\n        }, this);\n    }\n    // Handle unsupported games\n    if (gameType !== 'mines' && gameType !== 'dice' && gameType !== 'crash' && gameType !== 'limbo') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                className: \"bg-gray-800/80 border-gray-600 max-w-md mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                    className: \"p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: gameConfig.icon\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-4\",\n                            children: gameConfig.name\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"This game is coming soon! Stay tuned for updates.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                            onClick: handleBackToLobby,\n                            className: \"bg-purple-600 hover:bg-purple-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.ArrowLeft, {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this),\n                                \"Back to Lobby\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                    lineNumber: 355,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n            lineNumber: 353,\n            columnNumber: 7\n        }, this);\n    }\n    // Render Mines game\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-800/50 border-b border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleBackToLobby,\n                                        className: \"text-gray-400 hover:text-white hover:bg-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.ArrowLeft, {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Lobby\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.Gem, {\n                                                className: \"h-6 w-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: \"BetOctave\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700/50 px-3 py-1 rounded-md text-white text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: [\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_21__.formatCurrency)(user.usdt_balance),\n                                                    \" \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-400\",\n                                                children: \"₿\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowWallet(true),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white border-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.Wallet, {\n                                                className: \"h-4 w-4 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Wallet\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSoundEnabled(!soundEnabled),\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: soundEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.Volume2, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.VolumeX, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 67\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowHistory(true),\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.History, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowStats(true),\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.BarChart3, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: logout,\n                                        className: \"border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.LogOut, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                    lineNumber: 374,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-6\",\n                children: gameType === 'crash' ? // Crash Game Layout\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 order-2 lg:order-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_crash_CrashControls__WEBPACK_IMPORTED_MODULE_14__.CrashControls, {\n                                user: user,\n                                gameState: gameState,\n                                onStartGame: handleStartCrashGame,\n                                onCashOut: handleCrashCashOut,\n                                loading: gameLoading,\n                                canPlaceBet: crashGameContext.canPlaceBet(),\n                                canCashOut: crashGameContext.canCashOut(),\n                                currentMultiplier: crashGameContext.getCurrentMultiplier(),\n                                roundPhase: crashGameContext.getRoundPhase(),\n                                timeUntilNextRound: crashGameContext.getTimeUntilNextRound(),\n                                getCrashStats: crashGameContext.getCrashStats\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 456,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 455,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 order-1 lg:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_crash_CrashDisplay__WEBPACK_IMPORTED_MODULE_15__.CrashDisplay, {\n                                gameState: gameState,\n                                currentMultiplier: crashGameContext.getCurrentMultiplier(),\n                                roundPhase: crashGameContext.getRoundPhase(),\n                                timeElapsed: crashGameContext.getTimeElapsed(),\n                                timeUntilNextRound: crashGameContext.getTimeUntilNextRound()\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                    lineNumber: 453,\n                    columnNumber: 11\n                }, this) : gameType === 'dice' ? // Dice Game Layout\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 order-2 lg:order-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_dice_DiceControls__WEBPACK_IMPORTED_MODULE_12__.DiceControls, {\n                                user: user,\n                                gameState: gameState,\n                                onStartGame: handleStartDiceGame,\n                                onRollDice: handleRollDice,\n                                loading: gameLoading,\n                                canRollDice: diceGameContext.canRollDice(),\n                                calculateWinChance: diceGameContext.calculateWinChance,\n                                calculateMultiplier: diceGameContext.calculateMultiplier\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 487,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 486,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 order-1 lg:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/80 rounded-xl border border-gray-600 p-6 backdrop-blur-sm shadow-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-lg\",\n                                                            children: gameConfig.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: gameConfig.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"BetOctave Originals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 19\n                                            }, this),\n                                            gameState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Game ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-300 font-mono\",\n                                                        children: [\n                                                            \"#\",\n                                                            ((_gameState_id = gameState.id) === null || _gameState_id === void 0 ? void 0 : _gameState_id.toString().slice(-8)) || 'N/A'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_dice_DiceDisplay__WEBPACK_IMPORTED_MODULE_13__.DiceDisplay, {\n                                        gameState: gameState,\n                                        loading: gameLoading\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowStats(true),\n                                                className: \"text-gray-400 hover:text-white hover:bg-gray-700/50 p-2 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.BarChart3, {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Provably Fair\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 500,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                    lineNumber: 484,\n                    columnNumber: 11\n                }, this) : gameType === 'limbo' ? // Limbo Game Layout\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 order-2 lg:order-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_limbo_LimboControls__WEBPACK_IMPORTED_MODULE_16__.LimboControls, {\n                                user: user,\n                                gameState: gameState,\n                                onPlaceBet: handleLimboPlaceBet,\n                                loading: gameLoading,\n                                canPlaceBet: limboGameContext.canPlaceBet(),\n                                calculateWinChance: limboGameContext.calculateWinChance,\n                                calculatePayout: limboGameContext.calculatePayout\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 550,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 549,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 order-1 lg:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/80 rounded-xl border border-gray-600 p-6 backdrop-blur-sm shadow-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-lg\",\n                                                            children: gameConfig.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: gameConfig.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"BetOctave Originals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 19\n                                            }, this),\n                                            gameState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Game ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-300 font-mono\",\n                                                        children: [\n                                                            \"#\",\n                                                            ((_gameState_id1 = gameState.id) === null || _gameState_id1 === void 0 ? void 0 : _gameState_id1.toString().slice(-8)) || 'N/A'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_limbo_LimboDisplay__WEBPACK_IMPORTED_MODULE_17__.LimboDisplay, {\n                                        gameState: gameState,\n                                        loading: gameLoading,\n                                        lastResult: limboGameContext.getLastResult()\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowStats(true),\n                                                className: \"text-gray-400 hover:text-white hover:bg-gray-700/50 p-2 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.BarChart3, {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Provably Fair\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 563,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 562,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                    lineNumber: 547,\n                    columnNumber: 11\n                }, this) : // Mines Game Layout\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 order-2 lg:order-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_GameControls__WEBPACK_IMPORTED_MODULE_11__.GameControls, {\n                                user: user,\n                                gameState: gameState,\n                                onStartGame: handleStartGame,\n                                onCashOut: handleCashOut,\n                                loading: gameLoading\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 613,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 612,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 order-1 lg:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/80 rounded-xl border border-gray-600 p-6 backdrop-blur-sm shadow-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-lg\",\n                                                            children: gameConfig.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: gameConfig.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                                lineNumber: 632,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"BetOctave Originals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 19\n                                            }, this),\n                                            gameState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Game ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-300 font-mono\",\n                                                        children: [\n                                                            \"#\",\n                                                            ((_gameState_id2 = gameState.id) === null || _gameState_id2 === void 0 ? void 0 : _gameState_id2.toString().slice(-8)) || 'N/A'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 rounded-lg p-6 border border-gray-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_GameGrid__WEBPACK_IMPORTED_MODULE_10__.GameGrid, {\n                                            gameState: gameState,\n                                            onCellClick: handleCellClick,\n                                            loading: gameLoading\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowStats(true),\n                                                className: \"text-gray-400 hover:text-white hover:bg-gray-700/50 p-2 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Gem_History_LogOut_Volume2_VolumeX_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_26__.BarChart3, {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Fairness\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                                lineNumber: 624,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                            lineNumber: 623,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                    lineNumber: 610,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletModal__WEBPACK_IMPORTED_MODULE_18__.WalletModal, {\n                user: user,\n                isOpen: showWallet,\n                onClose: ()=>setShowWallet(false),\n                onBalanceUpdate: refreshUserData\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 675,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_GameHistory__WEBPACK_IMPORTED_MODULE_19__.GameHistory, {\n                isOpen: showHistory,\n                onClose: ()=>setShowHistory(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 682,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_LiveStats__WEBPACK_IMPORTED_MODULE_20__.LiveStats, {\n                isOpen: showStats,\n                onClose: ()=>setShowStats(false),\n                refreshTrigger: statsRefreshTrigger\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 687,\n                columnNumber: 7\n            }, this),\n            gameMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_GameMessage__WEBPACK_IMPORTED_MODULE_23__.GameMessage, {\n                type: gameMessage.type,\n                visible: gameMessage.visible,\n                multiplier: gameMessage.multiplier,\n                profit: gameMessage.profit,\n                onComplete: hideGameMessage\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 695,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_24__.Toaster, {}, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n                lineNumber: 705,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\game\\\\[gameType].tsx\",\n        lineNumber: 371,\n        columnNumber: 5\n    }, this);\n}\n_s(GamePage, \"aXQsR5nqiKWXBZxoPDAbxICUbJs=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_4__.useMinesGame,\n        _contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_5__.useDiceGame,\n        _contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_6__.useCrashGame,\n        _contexts_LimboGameContext__WEBPACK_IMPORTED_MODULE_7__.useLimboGame,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_25__.useToast\n    ];\n});\n_c = GamePage;\nvar _c;\n$RefreshReg$(_c, \"GamePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/game/[gameType].tsx\n"));

/***/ })

});